'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { FileUpload } from '@/components/data/FileUpload';
import { FileManager } from '@/components/data/FileManager';
import { EditableDataGrid } from '@/components/data/EditableDataGrid';
import { 
  getDataForEditing, 
  updateCell, 
  addRow, 
  deleteRow, 
  addColumn, 
  deleteColumn 
} from '@/lib/api';

export default function PlaygroundPage() {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [activeFileId, setActiveFileId] = useState<string | null>(null);
  const [uploadedData, setUploadedData] = useState<any>(null);
  const [gridData, setGridData] = useState<any[]>([]);
  const [columns, setColumns] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRows, setTotalRows] = useState(0);
  const pageSize = 50;

  // Load session data from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedSessionId = localStorage.getItem('sessionId');
      const storedActiveFileId = localStorage.getItem('activeFileId');
      
      if (storedSessionId) {
        setSessionId(storedSessionId);
        if (storedActiveFileId) {
          setActiveFileId(storedActiveFileId);
        }
      }
    }
  }, []);

  // Load data when session and file are available
  useEffect(() => {
    if (sessionId && activeFileId) {
      loadGridData();
    }
  }, [sessionId, activeFileId, currentPage]);

  const loadGridData = async () => {
    if (!sessionId) return;

    try {
      setLoading(true);
      setError(null);
      
      const response = await getDataForEditing(sessionId, activeFileId, currentPage, pageSize);
      
      setGridData(response.data || []);
      setColumns(response.columns || []);
      setTotalPages(response.total_pages || 1);
      setTotalRows(response.total_rows || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
      console.error('Error loading grid data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleUploadSuccess = (data: any) => {
    setUploadedData(data);
    setSessionId(data.session_id);
    setActiveFileId(data.files?.[0]?.file_id || data.file_id);
    setError(null);

    // Store in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('sessionId', data.session_id);
      if (data.files?.[0]?.file_id || data.file_id) {
        localStorage.setItem('activeFileId', data.files?.[0]?.file_id || data.file_id);
      }
    }
  };

  const handleFileSelect = (fileId: string) => {
    setActiveFileId(fileId);
    setCurrentPage(1); // Reset to first page when switching files
    setError(null);

    // Store in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('activeFileId', fileId);
    }
  };

  const handleCellUpdate = async (rowIndex: number, column: string, value: any) => {
    if (!sessionId) throw new Error('No session available');
    
    // Calculate actual row index considering pagination
    const actualRowIndex = (currentPage - 1) * pageSize + rowIndex;
    
    await updateCell(sessionId, actualRowIndex, column, value, activeFileId);
    
    // Update local data
    setGridData(prev => {
      const newData = [...prev];
      if (newData[rowIndex]) {
        newData[rowIndex] = { ...newData[rowIndex], [column]: value };
      }
      return newData;
    });
  };

  const handleAddRow = async (data: Record<string, any>) => {
    if (!sessionId) throw new Error('No session available');
    
    await addRow(sessionId, data, 'end', activeFileId);
    
    // Reload data to get the new row
    await loadGridData();
  };

  const handleDeleteRow = async (rowIndex: number) => {
    if (!sessionId) throw new Error('No session available');
    
    // Calculate actual row index considering pagination
    const actualRowIndex = (currentPage - 1) * pageSize + rowIndex;
    
    await deleteRow(sessionId, actualRowIndex, activeFileId);
    
    // Reload data to reflect the deletion
    await loadGridData();
  };

  const handleAddColumn = async (columnName: string, dataType: string) => {
    if (!sessionId) throw new Error('No session available');
    
    await addColumn(sessionId, columnName, dataType, undefined, 'end', activeFileId);
    
    // Reload data to get the new column
    await loadGridData();
  };

  const handleDeleteColumn = async (columnName: string) => {
    if (!sessionId) throw new Error('No session available');
    
    if (!confirm(`Are you sure you want to delete column "${columnName}"? This action cannot be undone.`)) {
      return;
    }
    
    await deleteColumn(sessionId, columnName, activeFileId);
    
    // Reload data to reflect the deletion
    await loadGridData();
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleExportData = () => {
    // This would implement data export functionality
    alert('Export functionality would be implemented here');
  };

  if (!sessionId) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-green-600 to-emerald-700 rounded-lg shadow-md p-6 mb-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl font-bold text-white">Data Editor Playground</h1>
            <p className="mt-2 text-lg text-green-100">
              Upload your data to start editing and exploring in a spreadsheet-like interface
            </p>
          </div>
        </div>

        <Card title="Upload Your Data">
          <FileUpload onUploadSuccess={handleUploadSuccess} />
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-green-600 to-emerald-700 rounded-lg shadow-md p-6 mb-8">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-3xl font-bold text-white">Data Editor Playground</h1>
          <p className="mt-2 text-lg text-green-100">
            Edit your data directly in a spreadsheet-like interface
          </p>
          {activeFileId && (
            <p className="mt-1 text-sm text-green-200">
              Working with file: {activeFileId.slice(0, 8)}...
            </p>
          )}
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-md shadow-sm">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm font-medium text-red-700">{error}</p>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* File Manager Sidebar */}
        <div className="lg:col-span-1">
          <Card title="File Manager">
            {sessionId && (
              <FileManager
                sessionId={sessionId}
                onFileSelect={handleFileSelect}
                activeFileId={activeFileId}
              />
            )}
          </Card>

          {/* Upload Additional Files */}
          <div className="mt-4">
            <Card title="Upload Additional Files">
              <FileUpload onUploadSuccess={handleUploadSuccess} sessionId={sessionId} />
            </Card>
          </div>
        </div>

        {/* Main Editor */}
        <div className="lg:col-span-3">
          {activeFileId && gridData.length > 0 ? (
            <Card title="Data Editor">
              {/* Pagination and Actions */}
              <div className="mb-4 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                    variant="outline"
                    className="text-sm"
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-600">
                    Page {currentPage} of {totalPages} ({totalRows} total rows)
                  </span>
                  <Button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    variant="outline"
                    className="text-sm"
                  >
                    Next
                  </Button>
                </div>
                <Button
                  onClick={handleExportData}
                  variant="outline"
                  className="text-sm"
                >
                  Export Data
                </Button>
              </div>

              {/* Editable Data Grid */}
              <EditableDataGrid
                data={gridData}
                columns={columns}
                onCellUpdate={handleCellUpdate}
                onAddRow={handleAddRow}
                onDeleteRow={handleDeleteRow}
                onAddColumn={handleAddColumn}
                onDeleteColumn={handleDeleteColumn}
                loading={loading}
              />
            </Card>
          ) : (
            <Card title="No Data">
              <div className="text-center py-8">
                <p className="text-gray-500">
                  {loading ? 'Loading data...' : 'Select a file from the file manager to start editing'}
                </p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
